<?php


use App\Http\Controllers\Pages\RolesController;
use App\Http\Controllers\Pages\SettingsController;
use App\Http\Controllers\Pages\UsersController;
use App\Http\Controllers\Pages\ProductController;
use App\Http\Controllers\Pages\CategoryController;
use App\Http\Controllers\Pages\BrandController;
use App\Http\Controllers\Pages\AttributeController;
use App\Http\Controllers\Pages\WarehouseController;
use App\Http\Controllers\Pages\WarehouseAreaController;
use App\Http\Controllers\Pages\WarehouseInventoryController;
use App\Http\Controllers\Pages\WarehouseReportController;
use App\Http\Controllers\Pages\PurchaseOrderController;
use App\Http\Controllers\Pages\SupplierController;
use App\Http\Controllers\Pages\InventoryTransactionController;
use App\Http\Controllers\Pages\GoodReceiptController;
use App\Http\Controllers\Pages\ActivityLogController;
use App\Http\Controllers\Pages\WarehouseTransferController;
use App\Http\Controllers\Pages\WarehouseTransferSerialController;
use App\Http\Controllers\Pages\WarehouseReceiptController;
use App\Http\Controllers\Pages\WarehouseTransferRequestController;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Route;
use Mcamara\LaravelLocalization\Facades\LaravelLocalization;

Route::group(['prefix' => LaravelLocalization::setLocale(),
  'middleware' => [
    'auth', 'localeSessionRedirect', 'localizationRedirect', 'localeViewPath',
  ]], function () {
  Route::prefix('admin')
    ->group(function () {

      Route::get('/dashboard', function () {
        return view('/');
      })->name('dashboard');

      // Test route
      Route::get('/test-create-product', [\App\Http\Controllers\TestController::class, 'testCreateProduct'])->name('test.create.product');

      Route::middleware('permission:users.list')->get('/users', [UsersController::class, 'list'])->name('users.list');

      Route::prefix('roles')
        ->middleware('permission:roles.list')
        ->group(function () {
          Route::middleware('permission:roles.list')->get('/', [RolesController::class, 'index'])->name('roles.list');
          Route::middleware('permission:roles.create')->get('/create', [RolesController::class, 'create'])->name('roles.create');
          Route::middleware('permission:roles.create')->post('/store', [RolesController::class, 'store'])->name('roles.store');
          Route::middleware('permission:roles.edit')->get('/edit/{role_id}', [RolesController::class, 'edit'])->name('roles.edit');
          Route::middleware('permission:roles.edit')->patch('/update/{role_id}', [RolesController::class, 'update'])->name('roles.update');
          Route::middleware('permission:roles.delete')->delete('/delete/{role_id}', [RolesController::class, 'delete'])->name('roles.delete');
        });
      Route::prefix('settings')
        ->middleware('permission:settings.list')
        ->group(function () {
          Route::middleware('permission:settings.list')->get('/general', [SettingsController::class, 'index'])->name('settings.list');
          Route::middleware('permission:settings.authorize')->get('/authorize', [SettingsController::class, 'platformAuthorize'])->name('settings.authorize');
          Route::middleware('permission:settings.cache')->get('/cache-permission-clear', [SettingsController::class, 'cachePermissionClear'])->name('settings.cache.permission.clear');
          Route::middleware('permission:settings.cache')->get('/cache-clear', [SettingsController::class, 'cacheClear'])->name('settings.cache.clear');
          Route::middleware('permission:settings.health')->get('/health', [SettingsController::class, 'health'])->name('settings.health');

          /*Update settings*/
          Route::middleware('permission:settings.update.general')->post('/update/general', [SettingsController::class, 'updateGeneral'])->name('settings.update.general');
          Route::middleware('permission:settings.update.smtp')->post('/update/smtp', [SettingsController::class, 'updateSMTP'])->name('settings.update.smtp');
          Route::middleware('permission:settings.update.smtp')->post('/smtp/test-config', [SettingsController::class, 'testSmtp'])->name('settings.smtp.test');
          Route::middleware('permission:settings.update.notification')->post('/update/notification', [SettingsController::class, 'updateNotification'])->name('settings.update.notification');
          Route::middleware('permission:settings.update.notification')->get('/notification/test-config', [SettingsController::class, 'testBroadcast'])->name('settings.notification.test');
          Route::middleware('permission:settings.update.vnpt')->post('/update/vnpt', [SettingsController::class, 'updateVNPT'])->name('settings.update.vnpt');
          Route::middleware('permission:settings.update.nhattin')->post('/update/nhattin', [SettingsController::class, 'updateNhattin'])->name('settings.update.nhattin');
          Route::middleware('permission:settings.update.sap')->post('/update/sap', [SettingsController::class, 'updateSAP'])->name('settings.update.sap');
          Route::middleware('permission:settings.update.zalo')->post('/update/zalo', [SettingsController::class, 'updateZALO'])->name('settings.update.zalo');
          Route::middleware('permission:settings.update.asus')->post('/update/asus', [SettingsController::class, 'updateASUS'])->name('settings.update.asus');
          Route::middleware('permission:settings.update.product')->post('/product', [SettingsController::class, 'updateProduct'])->name('settings.update.product');
        });

      // Supplier routes
      Route::prefix('suppliers')
        ->middleware('permission:suppliers.list')
        ->group(function () {
          // Supplier management
          Route::middleware('permission:suppliers.list')->get('/', [SupplierController::class, 'index'])->name('suppliers.list');
          Route::middleware('permission:suppliers.create')->get('/create', [SupplierController::class, 'create'])->name('suppliers.create');
          Route::middleware('permission:suppliers.create')->post('/store', [SupplierController::class, 'store'])->name('suppliers.store');
          Route::middleware('permission:suppliers.view')->get('/{id}', [SupplierController::class, 'show'])->name('suppliers.show');
          Route::middleware('permission:suppliers.edit')->get('/{id}/edit', [SupplierController::class, 'edit'])->name('suppliers.edit');
          Route::middleware('permission:suppliers.edit')->patch('/{id}/update', [SupplierController::class, 'update'])->name('suppliers.update');
          Route::middleware('permission:suppliers.delete')->delete('/{id}/delete', [SupplierController::class, 'delete'])->name('suppliers.delete');
          Route::middleware('permission:suppliers.list')->post('/datatable', [SupplierController::class, 'getDatatable'])->name('suppliers.datatable');
        });

      // Activity Log routes
      Route::prefix('activity-logs')
        ->middleware('permission:activity-logs.list')
        ->group(function () {
          Route::middleware('permission:activity-logs.list')->get('/', [ActivityLogController::class, 'index'])->name('activity-logs.list');
          Route::middleware('permission:activity-logs.list')->get('/data', [ActivityLogController::class, 'getActivityLogs'])->name('activity-logs.data');
        });

      // Product routes
      Route::prefix('products')
        ->middleware('permission:products.list')
        ->group(function () {
          // Product management
          Route::middleware('permission:products.list')->get('/', [ProductController::class, 'index'])->name('products.list');
          Route::middleware('permission:products.create')->get('/create', [ProductController::class, 'create'])->name('products.create');
          Route::middleware('permission:products.create')->get('/create-classic', [ProductController::class, 'createClassic'])->name('products.create.classic');
          Route::post('/store', [ProductController::class, 'store'])->name('products.store');
          Route::middleware('permission:products.edit')->get('/edit/{id}', [ProductController::class, 'edit'])->name('products.edit');
          Route::middleware('permission:products.edit')->get('/edit-wizard/{id}', [ProductController::class, 'editWizard'])->name('products.edit.wizard');
          Route::middleware('permission:products.edit')->patch('/update/{id}', [ProductController::class, 'update'])->name('products.update');
          Route::middleware('permission:products.delete')->delete('/delete/{id}', [ProductController::class, 'delete'])->name('products.delete');
          Route::middleware('permission:products.list')->post('/datatable', [ProductController::class, 'getDatatable'])->name('products.datatable');
          Route::middleware('permission:products.list')->get('/get-details/{id}', [ProductController::class, 'getDetails'])->name('products.get_details');
          Route::get('/search', [ProductController::class, 'search'])->name('products.search');

          // Product import/export
          Route::middleware('permission:products.import')->get('/import', [ProductController::class, 'importForm'])->name('products.import');
          Route::middleware('permission:products.import')->post('/import', [ProductController::class, 'import'])->name('products.import_submit');
          Route::middleware('permission:products.import')->get('/download-template', [ProductController::class, 'downloadTemplate'])->name('products.download_template');
          Route::middleware('permission:products.export')->get('/export-form', [ProductController::class, 'exportForm'])->name('products.export_form');
          Route::middleware('permission:products.export')->get('/export', [ProductController::class, 'export'])->name('products.export');

          // Product image upload
          Route::post('/upload-image', [ProductController::class, 'uploadImage'])->name('products.upload_image');
          Route::post('/upload-import-file', [ProductController::class, 'uploadImportFile'])->name('products.upload_import_file');
          Route::post('/process-import', [ProductController::class, 'processImport'])->name('products.process_import');
        });

      // Category routes
      Route::prefix('categories')
        ->middleware('permission:categories.list')
        ->group(function () {
          Route::middleware('permission:categories.list')->get('/', [CategoryController::class, 'index'])->name('categories.list');
          Route::middleware('permission:categories.create')->get('/create', [CategoryController::class, 'create'])->name('categories.create');
          Route::middleware('permission:categories.create')->post('/store', [CategoryController::class, 'store'])->name('categories.store');
          Route::middleware('permission:categories.edit')->get('/edit/{id}', [CategoryController::class, 'edit'])->name('categories.edit');
          Route::middleware('permission:categories.edit')->patch('/update/{id}', [CategoryController::class, 'update'])->name('categories.update');
          Route::middleware('permission:categories.delete')->delete('/delete/{id}', [CategoryController::class, 'delete'])->name('categories.delete');
          Route::middleware('permission:categories.list')->post('/datatable', [CategoryController::class, 'getDatatable'])->name('categories.datatable');
        });

      // Brand routes
      Route::prefix('brands')
        ->middleware('permission:brands.list')
        ->group(function () {
          Route::middleware('permission:brands.list')->get('/', [BrandController::class, 'index'])->name('brands.list');
          Route::middleware('permission:brands.create')->get('/create', [BrandController::class, 'create'])->name('brands.create');
          Route::middleware('permission:brands.create')->post('/store', [BrandController::class, 'store'])->name('brands.store');
          Route::middleware('permission:brands.edit')->get('/edit/{id}', [BrandController::class, 'edit'])->name('brands.edit');
          Route::middleware('permission:brands.edit')->patch('/update/{id}', [BrandController::class, 'update'])->name('brands.update');
          Route::middleware('permission:brands.delete')->delete('/delete/{id}', [BrandController::class, 'delete'])->name('brands.delete');
          Route::middleware('permission:brands.list')->post('/datatable', [BrandController::class, 'getDatatable'])->name('brands.datatable');

          // Brand logo upload
          Route::post('/upload-logo', [BrandController::class, 'uploadLogo'])->name('brands.upload_logo');
        });

      // API routes for product attributes (no permission check)
      Route::prefix('api')->group(function () {
        Route::get('/product-attributes/available', [AttributeController::class, 'getActiveAttributes']);
        Route::get('/products/{productId}/attributes', [ProductController::class, 'getProductAttributes']);

        // Products search for select2 moved to products route group

        // IMEI/Serial routes moved to product-serials route group
      });

      // Attribute routes
      Route::prefix('attributes')
        ->middleware('permission:attributes.list')
        ->group(function () {
          Route::middleware('permission:attributes.list')->get('/', [AttributeController::class, 'index'])->name('attributes.list');
          Route::middleware('permission:attributes.create')->get('/create', [AttributeController::class, 'create'])->name('attributes.create');
          Route::middleware('permission:attributes.create')->post('/store', [AttributeController::class, 'store'])->name('attributes.store');
          Route::middleware('permission:attributes.edit')->get('/edit/{id}', [AttributeController::class, 'edit'])->name('attributes.edit');
          Route::middleware('permission:attributes.edit')->patch('/update/{id}', [AttributeController::class, 'update'])->name('attributes.update');
          Route::middleware('permission:attributes.delete')->delete('/delete/{id}', [AttributeController::class, 'delete'])->name('attributes.delete');
          Route::middleware('permission:attributes.list')->post('/datatable', [AttributeController::class, 'getDatatable'])->name('attributes.datatable');
          Route::middleware('permission:attributes.list')->get('/{id}', [AttributeController::class, 'show'])->name('attributes.show');

          // Attribute values
          Route::middleware('permission:attributes.values.create')->post('/{attributeId}/values', [AttributeController::class, 'storeValue'])->name('attributes.values.store');
          Route::middleware('permission:attributes.values.edit')->patch('/{attributeId}/values/{valueId}', [AttributeController::class, 'updateValue'])->name('attributes.values.update');
          Route::middleware('permission:attributes.values.delete')->delete('/{attributeId}/values/{valueId}', [AttributeController::class, 'deleteValue'])->name('attributes.values.delete');
          Route::middleware('permission:attributes.values.list')->get('/{attributeId}/values', [AttributeController::class, 'getValues'])->name('attributes.values.list');

          // Get active attributes
          Route::middleware('permission:attributes.list')->get('/active/list', [AttributeController::class, 'getActiveAttributes'])->name('attributes.active');

          // Get active attributes for product creation/editing (no permission check)
          Route::get('/product-attributes/available', [AttributeController::class, 'getActiveAttributes']);
        });

      // Warehouse routes
      Route::prefix('warehouses')
        ->middleware('permission:warehouses.list')
        ->group(function () {
          // Warehouse management
          Route::middleware('permission:warehouses.list')->get('/', [WarehouseController::class, 'index'])->name('warehouses.list');
          Route::middleware('permission:warehouses.create')->get('/create', [WarehouseController::class, 'create'])->name('warehouses.create');
          Route::middleware('permission:warehouses.create')->post('/store', [WarehouseController::class, 'store'])->name('warehouses.store');
          Route::middleware('permission:warehouses.edit')->get('/edit/{id}', [WarehouseController::class, 'edit'])->name('warehouses.edit');
          Route::middleware('permission:warehouses.edit')->patch('/update/{id}', [WarehouseController::class, 'update'])->name('warehouses.update');
          Route::middleware('permission:warehouses.delete')->delete('/delete/{id}', [WarehouseController::class, 'delete'])->name('warehouses.delete');
          Route::middleware('permission:warehouses.set_default')->post('/set-default/{id}', [WarehouseController::class, 'setDefault'])->name('warehouses.set_default');
          Route::middleware('permission:warehouses.list')->post('/datatable', [WarehouseController::class, 'getDatatable'])->name('warehouses.datatable');

          // Warehouse areas
          Route::middleware('permission:warehouses.areas.list')->get('/areas', [WarehouseAreaController::class, 'index'])->name('warehouses.areas.list');
          Route::middleware('permission:warehouses.areas.create')->get('/areas/create', [WarehouseAreaController::class, 'create'])->name('warehouses.areas.create');
          Route::middleware('permission:warehouses.areas.create')->post('/areas/store', [WarehouseAreaController::class, 'store'])->name('warehouses.areas.store');
          Route::middleware('permission:warehouses.areas.edit')->get('/areas/edit/{id}', [WarehouseAreaController::class, 'edit'])->name('warehouses.areas.edit');
          Route::middleware('permission:warehouses.areas.edit')->patch('/areas/update/{id}', [WarehouseAreaController::class, 'update'])->name('warehouses.areas.update');
          Route::middleware('permission:warehouses.areas.delete')->delete('/areas/delete/{id}', [WarehouseAreaController::class, 'delete'])->name('warehouses.areas.delete');
          Route::middleware('permission:warehouses.areas.list')->post('/areas/datatable', [WarehouseAreaController::class, 'getDatatable'])->name('warehouses.areas.datatable');

          // Inventory management
          Route::middleware('permission:warehouses.inventory.view')->get('/inventory', [WarehouseInventoryController::class, 'index'])->name('warehouses.inventory.view');
          Route::middleware('permission:warehouses.inventory.add')->get('/inventory/add', [WarehouseInventoryController::class, 'add'])->name('warehouses.inventory.add');
          Route::middleware('permission:warehouses.inventory.add')->post('/inventory/add', [WarehouseInventoryController::class, 'store'])->name('warehouses.inventory.store');
          Route::middleware('permission:warehouses.inventory.remove')->get('/inventory/remove', [WarehouseInventoryController::class, 'remove'])->name('warehouses.inventory.remove');
          Route::middleware('permission:warehouses.inventory.remove')->post('/inventory/remove', [WarehouseInventoryController::class, 'processRemove'])->name('warehouses.inventory.process_remove');
          Route::middleware('permission:warehouses.inventory.transfer')->get('/inventory/transfer', [WarehouseInventoryController::class, 'transfer'])->name('warehouses.inventory.transfer');
          Route::middleware('permission:warehouses.inventory.transfer')->post('/inventory/transfer', [WarehouseInventoryController::class, 'processTransfer'])->name('warehouses.inventory.process_transfer');
          Route::middleware('permission:warehouses.inventory.adjust')->get('/inventory/adjust', [WarehouseInventoryController::class, 'adjust'])->name('warehouses.inventory.adjust');
          Route::middleware('permission:warehouses.inventory.adjust')->post('/inventory/adjust', [WarehouseInventoryController::class, 'processAdjust'])->name('warehouses.inventory.process_adjust');
          Route::middleware('permission:warehouses.inventory.view')->post('/inventory/datatable', [WarehouseInventoryController::class, 'getDatatable'])->name('warehouses.inventory.datatable');

          // Inventory API endpoints
          Route::middleware('permission:warehouses.inventory.view')->get('/inventory/products', [WarehouseInventoryController::class, 'getProductsInWarehouse'])->name('warehouses.inventory.products');
          Route::middleware('permission:warehouses.inventory.view')->get('/inventory/product-details', [WarehouseInventoryController::class, 'getProductDetails'])->name('warehouses.inventory.product_details');

          // Purchase Orders
          Route::middleware('permission:warehouses.purchase-orders.view')->get('/purchase-orders', [PurchaseOrderController::class, 'index'])->name('warehouses.purchase-orders.list');
          Route::middleware('permission:warehouses.purchase-orders.create')->get('/purchase-orders/create', [PurchaseOrderController::class, 'create'])->name('warehouses.purchase-orders.create');
          Route::middleware('permission:warehouses.purchase-orders.create')->post('/purchase-orders/store', [PurchaseOrderController::class, 'store'])->name('warehouses.purchase-orders.store');
          Route::middleware('permission:warehouses.purchase-orders.view')->get('/purchase-orders/{id}', [PurchaseOrderController::class, 'show'])->name('warehouses.purchase-orders.show');
          Route::middleware('permission:warehouses.purchase-orders.edit')->get('/purchase-orders/{id}/edit', [PurchaseOrderController::class, 'edit'])->name('warehouses.purchase-orders.edit');
          Route::middleware('permission:warehouses.purchase-orders.edit')->patch('/purchase-orders/{id}/update', [PurchaseOrderController::class, 'update'])->name('warehouses.purchase-orders.update');
          Route::middleware('permission:warehouses.purchase-orders.view')->post('/purchase-orders/datatable', [PurchaseOrderController::class, 'getDatatable'])->name('warehouses.purchase-orders.datatable');
          Route::middleware('permission:warehouses.purchase-orders.edit')->post('/purchase-orders/{id}/submit', [PurchaseOrderController::class, 'submitForApproval'])->name('warehouses.purchase-orders.submit');
          Route::middleware('permission:warehouses.purchase-orders.approve')->post('/purchase-orders/{id}/approve', [PurchaseOrderController::class, 'approve'])->name('warehouses.purchase-orders.approve');
          Route::middleware('permission:warehouses.purchase-orders.approve')->post('/purchase-orders/{id}/reject', [PurchaseOrderController::class, 'reject'])->name('warehouses.purchase-orders.reject');
          Route::middleware('permission:warehouses.purchase-orders.edit')->post('/purchase-orders/{id}/cancel', [PurchaseOrderController::class, 'cancel'])->name('warehouses.purchase-orders.cancel');
          Route::middleware('permission:warehouses.purchase-orders.receive')->get('/purchase-orders/{id}/receive', [PurchaseOrderController::class, 'receive'])->name('warehouses.purchase-orders.receive');
          Route::middleware('permission:warehouses.purchase-orders.receive')->post('/purchase-orders/{id}/process-receive', [PurchaseOrderController::class, 'processReceive'])->name('warehouses.purchase-orders.process_receive');
          Route::middleware('permission:warehouses.purchase-orders.view')->get('/purchase-orders/{id}/print', [PurchaseOrderController::class, 'printPurchaseOrder'])->name('warehouses.purchase-orders.print');
          Route::middleware('permission:warehouses.purchase-orders.view')->get('/purchase-orders/{id}/print-goods-receipt', [PurchaseOrderController::class, 'printGoodsReceipt'])->name('warehouses.purchase-orders.print_goods_receipt');
          Route::middleware('permission:warehouses.purchase-orders.view')->get('/purchase-orders/{id}/attachments', [PurchaseOrderController::class, 'getAttachments'])->name('warehouses.purchase-orders.attachments');
          Route::middleware('permission:warehouses.purchase-orders.edit')->post('/purchase-orders/{id}/upload-attachment', [PurchaseOrderController::class, 'uploadAttachment'])->name('warehouses.purchase-orders.upload_attachment');
          Route::middleware('permission:warehouses.purchase-orders.edit')->delete('/purchase-orders/{id}/attachments/{attachmentId}', [PurchaseOrderController::class, 'deleteAttachment'])->name('warehouses.purchase-orders.delete_attachment');
          Route::middleware('permission:warehouses.purchase-orders.view')->get('/purchase-orders/search/products', [PurchaseOrderController::class, 'searchProducts'])->name('warehouses.purchase-orders.search_products');
          Route::middleware('permission:warehouses.purchase-orders.view')->get('/purchase-orders/warehouse/{warehouseId}/areas', [PurchaseOrderController::class, 'getWarehouseAreas'])->name('warehouses.purchase-orders.warehouse_areas');
          Route::middleware('permission:warehouses.purchase-orders.receive')->get('/purchase-orders/{id}/create-good-receipt', [PurchaseOrderController::class, 'createGoodReceipt'])->name('warehouses.purchase-orders.create_good_receipt');

          // Good Receipts
          Route::middleware('permission:warehouses.good-receipts.view')->get('/good-receipts', [GoodReceiptController::class, 'index'])->name('warehouses.good-receipts.index');
          Route::middleware('permission:warehouses.good-receipts.view')->get('/good-receipts/{id}', [GoodReceiptController::class, 'show'])->name('warehouses.good-receipts.show');
          Route::middleware('permission:warehouses.good-receipts.view')->post('/good-receipts/datatable', [GoodReceiptController::class, 'getDatatable'])->name('warehouses.good-receipts.datatable');
          Route::middleware('permission:warehouses.good-receipts.view')->get('/good-receipts/{id}/items', [GoodReceiptController::class, 'getItems'])->name('warehouses.good-receipts.items');
          Route::middleware('permission:warehouses.good-receipts.edit')->post('/good-receipts/{id}/cancel', [GoodReceiptController::class, 'cancel'])->name('warehouses.good-receipts.cancel');
          Route::middleware('permission:warehouses.good-receipts.view')->get('/good-receipts/{id}/print', [GoodReceiptController::class, 'print'])->name('warehouses.good-receipts.print');
          Route::middleware('permission:warehouses.good-receipts.view')->get('/good-receipts/{id}/serials/{itemId}', [GoodReceiptController::class, 'getSerials'])->name('warehouses.good-receipts.serials');
          Route::middleware('permission:warehouses.good-receipts.edit')->post('/good-receipts/{id}/update-invoice', [GoodReceiptController::class, 'updateInvoice'])->name('warehouses.good-receipts.update_invoice');
          Route::middleware('permission:warehouses.good-receipts.edit')->post('/good-receipts/{id}/save-draft', [GoodReceiptController::class, 'saveDraft'])->name('warehouses.good-receipts.save_draft');
          Route::middleware('permission:warehouses.good-receipts.view')->get('/good-receipts/{id}/get-import-data', [GoodReceiptController::class, 'getImportData'])->name('warehouses.good-receipts.get_import_data');
          Route::middleware('permission:warehouses.good-receipts.edit')->post('/good-receipts/{id}/submit', [GoodReceiptController::class, 'submit'])->name('warehouses.good-receipts.submit');
          Route::middleware('permission:warehouses.good-receipts.approve')->post('/good-receipts/{id}/approve', [GoodReceiptController::class, 'approve'])->name('warehouses.good-receipts.approve');
          Route::middleware('permission:warehouses.good-receipts.edit')->post('/good-receipts/{id}/scan-imei/{itemId}', [GoodReceiptController::class, 'scanImei'])->name('warehouses.good-receipts.scan_imei');
          Route::middleware('permission:warehouses.good-receipts.edit')->post('/check-serial-exists', [GoodReceiptController::class, 'checkSerialExists'])->name('warehouses.good-receipts.check_serial_exists');
          Route::middleware('permission:warehouses.good-receipts.edit')->post('/check-serials-exist', [GoodReceiptController::class, 'checkSerialsExist'])->name('warehouses.good-receipts.check_serials_exist');
          Route::middleware('permission:warehouses.good-receipts.view')->get('/good-receipts/{id}/attachments', [GoodReceiptController::class, 'getAttachments'])->name('warehouses.good-receipts.attachments');
          Route::middleware('permission:warehouses.good-receipts.edit')->post('/good-receipts/{id}/upload-attachment', [GoodReceiptController::class, 'uploadAttachment'])->name('warehouses.good-receipts.upload_attachment');
          Route::middleware('permission:warehouses.good-receipts.edit')->delete('/good-receipts/{id}/attachments/{attachmentId}', [GoodReceiptController::class, 'deleteAttachment'])->name('warehouses.good-receipts.delete_attachment');
          Route::middleware('permission:warehouses.good-receipts.view')->get('/good-receipts/{id}/batches/{itemId}', [GoodReceiptController::class, 'getBatches'])->name('warehouses.good-receipts.batches');

          // Good Receipt Invoices
          Route::middleware('permission:warehouses.good-receipts.view')->get('/good-receipts/{id}/invoices', [\App\Http\Controllers\Pages\GoodReceiptInvoiceController::class, 'getInvoices'])->name('warehouses.good-receipts.invoices');
          Route::middleware('permission:warehouses.good-receipts.edit')->post('/good-receipts/{id}/invoices', [\App\Http\Controllers\Pages\GoodReceiptInvoiceController::class, 'store'])->name('warehouses.good-receipts.invoices.store');
          Route::middleware('permission:warehouses.good-receipts.view')->get('/good-receipts/invoices/{invoiceId}', [\App\Http\Controllers\Pages\GoodReceiptInvoiceController::class, 'getInvoice'])->name('warehouses.good-receipts.invoices.show');
          Route::middleware('permission:warehouses.good-receipts.edit')->put('/good-receipts/invoices/{invoiceId}', [\App\Http\Controllers\Pages\GoodReceiptInvoiceController::class, 'update'])->name('warehouses.good-receipts.invoices.update');
          Route::middleware('permission:warehouses.good-receipts.edit')->delete('/good-receipts/invoices/{invoiceId}', [\App\Http\Controllers\Pages\GoodReceiptInvoiceController::class, 'destroy'])->name('warehouses.good-receipts.invoices.delete');
          Route::middleware('permission:warehouses.good-receipts.view')->get('/good-receipts/invoices/{invoiceId}/attachments', [\App\Http\Controllers\Pages\GoodReceiptInvoiceController::class, 'getAttachments'])->name('warehouses.good-receipts.invoices.attachments');
          Route::middleware('permission:warehouses.good-receipts.edit')->delete('/good-receipts/invoices/{invoiceId}/attachments/{attachmentId}', [\App\Http\Controllers\Pages\GoodReceiptInvoiceController::class, 'deleteAttachment'])->name('warehouses.good-receipts.invoices.delete_attachment');

          // Reports
          Route::middleware('permission:warehouses.reports.view')->get('/reports', [WarehouseReportController::class, 'index'])->name('warehouses.reports.view');
          Route::middleware('permission:warehouses.reports.export')->get('/reports/export', [WarehouseReportController::class, 'export'])->name('warehouses.reports.export');

          // Inventory Transactions
          Route::middleware('permission:warehouses.transactions.view')->get('/transactions', [InventoryTransactionController::class, 'index'])->name('warehouses.transactions.index');
          Route::middleware('permission:warehouses.transactions.view')->get('/transactions/{id}', [InventoryTransactionController::class, 'show'])->name('warehouses.transactions.show');
          Route::middleware('permission:warehouses.transactions.view')->post('/transactions/datatable', [InventoryTransactionController::class, 'getDatatable'])->name('warehouses.transactions.datatable');
          Route::middleware('permission:warehouses.transactions.view')->get('/transactions/products', [InventoryTransactionController::class, 'getProducts'])->name('warehouses.transactions.products');
          Route::middleware('permission:warehouses.transactions.view')->get('/transactions/purchase-orders', [InventoryTransactionController::class, 'getPurchaseOrders'])->name('warehouses.transactions.purchase_orders');

          // Warehouse Transfer Requests
          Route::middleware('permission:warehouses.transfer_requests.view')->get('/transfer-requests', [WarehouseTransferRequestController::class, 'index'])->name('warehouses.transfer-requests.list');
          Route::middleware('permission:warehouses.transfer_requests.create')->get('/transfer-requests/create', [WarehouseTransferRequestController::class, 'create'])->name('warehouses.transfer-requests.create');
          Route::middleware('permission:warehouses.transfer_requests.create')->post('/transfer-requests/store', [WarehouseTransferRequestController::class, 'store'])->name('warehouses.transfer-requests.store');
          Route::middleware('permission:warehouses.transfer_requests.view')->get('/transfer-requests/{id}', [WarehouseTransferRequestController::class, 'show'])->name('warehouses.transfer-requests.show');
          Route::middleware('permission:warehouses.transfer_requests.edit')->get('/transfer-requests/{id}/edit', [WarehouseTransferRequestController::class, 'edit'])->name('warehouses.transfer-requests.edit');
          Route::middleware('permission:warehouses.transfer_requests.edit')->patch('/transfer-requests/{id}/update', [WarehouseTransferRequestController::class, 'update'])->name('warehouses.transfer-requests.update');
          Route::middleware('permission:warehouses.transfer_requests.edit')->post('/transfer-requests/{id}/submit', [WarehouseTransferRequestController::class, 'submit'])->name('warehouses.transfer-requests.submit');
          Route::middleware('permission:warehouses.transfer_requests.approve')->post('/transfer-requests/{id}/approve', [WarehouseTransferRequestController::class, 'approve'])->name('warehouses.transfer-requests.approve');
          Route::middleware('permission:warehouses.transfer_requests.approve')->post('/transfer-requests/{id}/reject', [WarehouseTransferRequestController::class, 'reject'])->name('warehouses.transfer-requests.reject');
          Route::middleware('permission:warehouses.transfer_requests.edit')->post('/transfer-requests/{id}/cancel', [WarehouseTransferRequestController::class, 'cancel'])->name('warehouses.transfer-requests.cancel');
          Route::middleware('permission:warehouses.transfer_requests.approve')->post('/transfer-requests/{id}/create-transfer', [WarehouseTransferRequestController::class, 'createTransfer'])->name('warehouses.transfer-requests.create-transfer');
          Route::middleware('permission:warehouses.transfer_requests.view')->post('/transfer-requests/datatable', [WarehouseTransferRequestController::class, 'getDatatable'])->name('warehouses.transfer-requests.datatable');
          Route::middleware('permission:warehouses.transfer_requests.view')->get('/transfer-requests/warehouse/{warehouseId}/products', [WarehouseTransferRequestController::class, 'getProductsInWarehouse'])->name('warehouses.transfer-requests.products');

          // Warehouse Transfers
          Route::middleware('permission:warehouses.transfers.view')->get('/transfers', [WarehouseTransferController::class, 'index'])->name('warehouses.transfers.list');
          // Phiếu chuyển kho chỉ được tạo từ phiếu yêu cầu chuyển hàng đã được duyệt
          Route::middleware('permission:warehouses.transfers.view')->get('/transfers/{id}', [WarehouseTransferController::class, 'show'])->name('warehouses.transfers.show');
          // Đã xóa route chỉnh sửa phiếu chuyển kho
          Route::middleware('permission:warehouses.transfers.edit')->post('/transfers/{id}/submit', [WarehouseTransferController::class, 'submit'])->name('warehouses.transfers.submit');
          Route::middleware('permission:warehouses.transfers.approve')->post('/transfers/{id}/approve', [WarehouseTransferController::class, 'approve'])->name('warehouses.transfers.approve');
          Route::middleware('permission:warehouses.transfers.edit')->post('/transfers/{id}/cancel', [WarehouseTransferController::class, 'cancel'])->name('warehouses.transfers.cancel');
          Route::middleware('permission:warehouses.transfers.edit')->post('/transfers/{id}/shipping', [WarehouseTransferController::class, 'createShipping'])->name('warehouses.transfers.shipping');
          Route::middleware('permission:warehouses.transfers.edit')->post('/transfers/{id}/receipt', [WarehouseTransferController::class, 'createReceipt'])->name('warehouses.transfers.receipt');
          Route::middleware('permission:warehouses.transfers.view')->post('/transfers/datatable', [WarehouseTransferController::class, 'getDatatable'])->name('warehouses.transfers.datatable');
          Route::middleware('permission:warehouses.transfers.view')->get('/transfers/warehouse/{warehouseId}/products', [WarehouseTransferController::class, 'getProductsInWarehouse'])->name('warehouses.transfers.products');
          Route::middleware('permission:warehouses.transfers.view')->get('/transfers/warehouse/{warehouseId}/product/{productId}/details', [WarehouseTransferController::class, 'getProductDetailsInWarehouse'])->name('warehouses.transfers.product_details');

          // Warehouse Transfer IMEI Scanning
          Route::middleware('permission:warehouses.transfers.view')->get('/transfers/{id}/scan-imei', [WarehouseTransferSerialController::class, 'showScanImei'])->name('warehouses.transfers.scan_imei');
          Route::middleware('permission:warehouses.transfers.view')->get('/transfers/{id}/serial-items', [WarehouseTransferSerialController::class, 'getSerialItems'])->name('warehouses.transfers.serial_items');
          Route::middleware('permission:warehouses.transfers.view')->get('/transfers/{id}/imeis', [WarehouseTransferSerialController::class, 'getScannedImeis'])->name('warehouses.transfers.imeis');
          Route::middleware('permission:warehouses.transfers.view')->get('/transfers/{id}/available-imeis', [WarehouseTransferSerialController::class, 'getAvailableImeis'])->name('warehouses.transfers.available_imeis');
          Route::middleware('permission:warehouses.transfers.edit')->post('/transfers/{id}/scan-imei', [WarehouseTransferSerialController::class, 'scanImei'])->name('warehouses.transfers.scan_imei_post');
          Route::middleware('permission:warehouses.transfers.edit')->delete('/transfers/{id}/imeis/{serialId}', [WarehouseTransferSerialController::class, 'deleteImei'])->name('warehouses.transfers.delete_imei');
          Route::middleware('permission:warehouses.transfers.edit')->post('/transfers/{id}/complete-imei-scan', [WarehouseTransferSerialController::class, 'completeImeiScan'])->name('warehouses.transfers.complete_imei_scan');
          Route::middleware('permission:warehouses.transfers.view')->get('/transfers/{id}/item-serials/{itemId}', [WarehouseTransferSerialController::class, 'getItemSerials'])->name('warehouses.transfers.item_serials');

          // Warehouse Receipts
          Route::middleware('permission:warehouses.receipts.view')->get('/receipts', [WarehouseReceiptController::class, 'index'])->name('warehouses.receipts.list');
          Route::middleware('permission:warehouses.receipts.view')->get('/receipts/{id}', [WarehouseReceiptController::class, 'show'])->name('warehouses.receipts.show');
          Route::middleware('permission:warehouses.receipts.edit')->post('/receipts/{id}/complete', [WarehouseReceiptController::class, 'complete'])->name('warehouses.receipts.complete');
          Route::middleware('permission:warehouses.receipts.edit')->post('/receipts/{id}/complete-without-imei', [WarehouseReceiptController::class, 'completeWithoutImei'])->name('warehouses.receipts.complete_without_imei');
          Route::middleware('permission:warehouses.receipts.edit')->post('/receipts/{id}/cancel', [WarehouseReceiptController::class, 'cancel'])->name('warehouses.receipts.cancel');
          Route::middleware('permission:warehouses.receipts.view')->post('/receipts/datatable', [WarehouseReceiptController::class, 'getDatatable'])->name('warehouses.receipts.datatable');
          Route::middleware('permission:warehouses.receipts.view')->get('/receipts/{id}/check-imei/{itemId}', [WarehouseReceiptController::class, 'checkImei'])->name('warehouses.receipts.check_imei');
          Route::middleware('permission:warehouses.receipts.edit')->post('/receipts/{id}/scan-imei/{itemId}', [WarehouseReceiptController::class, 'scanImei'])->name('warehouses.receipts.scan_imei');
          Route::middleware('permission:warehouses.receipts.view')->get('/receipts/{id}/item-serials/{itemId}', [WarehouseReceiptController::class, 'getItemSerials'])->name('warehouses.receipts.item_serials');
        });

        // Product Serials
        Route::prefix('product-serials')->name('product-serials.')->group(function () {
          Route::post('/check', [\App\Http\Controllers\Pages\ProductSerialController::class, 'checkImei'])->name('check');
          Route::post('/check-multiple', [\App\Http\Controllers\Pages\ProductSerialController::class, 'checkMultipleImeis'])->name('check-multiple');
          Route::post('/store', [\App\Http\Controllers\Pages\ProductSerialController::class, 'storeImeis'])->name('store')->withoutMiddleware(['permission:warehouses.purchase-orders.receive']);
          Route::get('/get', [\App\Http\Controllers\Pages\ProductSerialController::class, 'getImeis'])->name('get');
        });
    });
});